Collections:
- Name: DeepLabV3
  Metadata:
    Training Data:
    - Cityscapes
    - ADE20K
    - Pascal VOC 2012 + Aug
    - <PERSON> Context
    - Pascal Context 59
    - COCO-Stuff 10k
    - COCO-Stuff 164k
  Paper:
    URL: https://arxiv.org/abs/1706.05587
    Title: Rethinking atrous convolution for semantic image segmentation
  README: configs/deeplabv3/README.md
  Code:
    URL: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/aspp_head.py#L54
    Version: v0.17.0
  Converted From:
    Code: https://github.com/tensorflow/models/tree/master/research/deeplab
Models:
- Name: deeplabv3_r50-d8_512x1024_40k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,1024)
    lr schd: 40000
    inference time (ms/im):
    - value: 389.11
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 6.1
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.09
      mIoU(ms+flip): 80.45
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x1024_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x1024_40k_cityscapes/deeplabv3_r50-d8_512x1024_40k_cityscapes_20200605_022449-acadc2f8.pth
- Name: deeplabv3_r101-d8_512x1024_40k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,1024)
    lr schd: 40000
    inference time (ms/im):
    - value: 520.83
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 9.6
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.12
      mIoU(ms+flip): 79.61
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x1024_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x1024_40k_cityscapes/deeplabv3_r101-d8_512x1024_40k_cityscapes_20200605_012241-7fd3f799.pth
- Name: deeplabv3_r50-d8_769x769_40k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (769,769)
    lr schd: 40000
    inference time (ms/im):
    - value: 900.9
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 6.9
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.58
      mIoU(ms+flip): 79.89
  Config: configs/deeplabv3/deeplabv3_r50-d8_769x769_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_769x769_40k_cityscapes/deeplabv3_r50-d8_769x769_40k_cityscapes_20200606_113723-7eda553c.pth
- Name: deeplabv3_r101-d8_769x769_40k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (769,769)
    lr schd: 40000
    inference time (ms/im):
    - value: 1204.82
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 10.9
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.27
      mIoU(ms+flip): 80.11
  Config: configs/deeplabv3/deeplabv3_r101-d8_769x769_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_769x769_40k_cityscapes/deeplabv3_r101-d8_769x769_40k_cityscapes_20200606_113809-c64f889f.pth
- Name: deeplabv3_r18-d8_512x1024_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-18-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 72.57
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 1.7
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.7
      mIoU(ms+flip): 78.27
  Config: configs/deeplabv3/deeplabv3_r18-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18-d8_512x1024_80k_cityscapes/deeplabv3_r18-d8_512x1024_80k_cityscapes_20201225_021506-23dffbe2.pth
- Name: deeplabv3_r50-d8_512x1024_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,1024)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.32
      mIoU(ms+flip): 80.57
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x1024_80k_cityscapes/deeplabv3_r50-d8_512x1024_80k_cityscapes_20200606_113404-b92cfdd4.pth
- Name: deeplabv3_r101-d8_512x1024_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,1024)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.2
      mIoU(ms+flip): 81.21
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x1024_80k_cityscapes/deeplabv3_r101-d8_512x1024_80k_cityscapes_20200606_113503-9e428899.pth
- Name: deeplabv3_r101-d8_fp16_512x1024_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 259.07
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP16
      resolution: (512,1024)
    Training Memory (GB): 5.75
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.48
  Config: configs/deeplabv3/deeplabv3_r101-d8_fp16_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_fp16_512x1024_80k_cityscapes/deeplabv3_r101-d8_fp16_512x1024_80k_cityscapes_20200717_230920-774d9cec.pth
- Name: deeplabv3_r18-d8_769x769_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-18-D8
    crop size: (769,769)
    lr schd: 80000
    inference time (ms/im):
    - value: 180.18
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 1.9
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.6
      mIoU(ms+flip): 78.26
  Config: configs/deeplabv3/deeplabv3_r18-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18-d8_769x769_80k_cityscapes/deeplabv3_r18-d8_769x769_80k_cityscapes_20201225_021506-6452126a.pth
- Name: deeplabv3_r50-d8_769x769_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (769,769)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.89
      mIoU(ms+flip): 81.06
  Config: configs/deeplabv3/deeplabv3_r50-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_769x769_80k_cityscapes/deeplabv3_r50-d8_769x769_80k_cityscapes_20200606_221338-788d6228.pth
- Name: deeplabv3_r101-d8_769x769_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (769,769)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.67
      mIoU(ms+flip): 80.81
  Config: configs/deeplabv3/deeplabv3_r101-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_769x769_80k_cityscapes/deeplabv3_r101-d8_769x769_80k_cityscapes_20200607_013353-60e95418.pth
- Name: deeplabv3_r101-d16-mg124_512x1024_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D16-MG124
    crop size: (512,1024)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.36
      mIoU(ms+flip): 79.84
  Config: configs/deeplabv3/deeplabv3_r101-d16-mg124_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d16-mg124_512x1024_80k_cityscapes/deeplabv3_r101-d16-mg124_512x1024_80k_cityscapes_20200908_005644-57bb8425.pth
- Name: deeplabv3_r18b-d8_512x1024_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-18b-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 71.79
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 1.6
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 76.26
      mIoU(ms+flip): 77.88
  Config: configs/deeplabv3/deeplabv3_r18b-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18b-d8_512x1024_80k_cityscapes/deeplabv3_r18b-d8_512x1024_80k_cityscapes_20201225_094144-46040cef.pth
- Name: deeplabv3_r50b-d8_512x1024_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50b-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 364.96
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 6.0
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.63
      mIoU(ms+flip): 80.98
  Config: configs/deeplabv3/deeplabv3_r50b-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50b-d8_512x1024_80k_cityscapes/deeplabv3_r50b-d8_512x1024_80k_cityscapes_20201225_155148-ec368954.pth
- Name: deeplabv3_r101b-d8_512x1024_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101b-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 552.49
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 9.5
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.01
      mIoU(ms+flip): 81.21
  Config: configs/deeplabv3/deeplabv3_r101b-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101b-d8_512x1024_80k_cityscapes/deeplabv3_r101b-d8_512x1024_80k_cityscapes_20201226_171821-8fd49503.pth
- Name: deeplabv3_r18b-d8_769x769_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-18b-D8
    crop size: (769,769)
    lr schd: 80000
    inference time (ms/im):
    - value: 172.71
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 1.8
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 75.63
      mIoU(ms+flip): 77.51
  Config: configs/deeplabv3/deeplabv3_r18b-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r18b-d8_769x769_80k_cityscapes/deeplabv3_r18b-d8_769x769_80k_cityscapes_20201225_094144-fdc985d9.pth
- Name: deeplabv3_r50b-d8_769x769_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50b-D8
    crop size: (769,769)
    lr schd: 80000
    inference time (ms/im):
    - value: 862.07
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 6.8
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.8
      mIoU(ms+flip): 80.27
  Config: configs/deeplabv3/deeplabv3_r50b-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50b-d8_769x769_80k_cityscapes/deeplabv3_r50b-d8_769x769_80k_cityscapes_20201225_155404-87fb0cf4.pth
- Name: deeplabv3_r101b-d8_769x769_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101b-D8
    crop size: (769,769)
    lr schd: 80000
    inference time (ms/im):
    - value: 1219.51
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 10.7
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.41
      mIoU(ms+flip): 80.73
  Config: configs/deeplabv3/deeplabv3_r101b-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101b-d8_769x769_80k_cityscapes/deeplabv3_r101b-d8_769x769_80k_cityscapes_20201226_190843-9142ee57.pth
- Name: deeplabv3_r50-d8_512x512_80k_ade20k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 67.75
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 8.9
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 42.42
      mIoU(ms+flip): 43.28
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_80k_ade20k/deeplabv3_r50-d8_512x512_80k_ade20k_20200614_185028-0bb3f844.pth
- Name: deeplabv3_r101-d8_512x512_80k_ade20k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 98.62
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 12.4
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 44.08
      mIoU(ms+flip): 45.19
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_80k_ade20k/deeplabv3_r101-d8_512x512_80k_ade20k_20200615_021256-d89c7fa4.pth
- Name: deeplabv3_r50-d8_512x512_160k_ade20k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 42.66
      mIoU(ms+flip): 44.09
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_160k_ade20k/deeplabv3_r50-d8_512x512_160k_ade20k_20200615_123227-5d0ee427.pth
- Name: deeplabv3_r101-d8_512x512_160k_ade20k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 45.0
      mIoU(ms+flip): 46.66
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_160k_ade20k/deeplabv3_r101-d8_512x512_160k_ade20k_20200615_105816-b1f72b3b.pth
- Name: deeplabv3_r50-d8_512x512_20k_voc12aug
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 72.05
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.1
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 76.17
      mIoU(ms+flip): 77.42
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x512_20k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_20k_voc12aug/deeplabv3_r50-d8_512x512_20k_voc12aug_20200617_010906-596905ef.pth
- Name: deeplabv3_r101-d8_512x512_20k_voc12aug
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 101.94
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.6
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 78.7
      mIoU(ms+flip): 79.95
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x512_20k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_20k_voc12aug/deeplabv3_r101-d8_512x512_20k_voc12aug_20200617_010932-8d13832f.pth
- Name: deeplabv3_r50-d8_512x512_40k_voc12aug
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.68
      mIoU(ms+flip): 78.78
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x512_40k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_40k_voc12aug/deeplabv3_r50-d8_512x512_40k_voc12aug_20200613_161546-2ae96e7e.pth
- Name: deeplabv3_r101-d8_512x512_40k_voc12aug
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.92
      mIoU(ms+flip): 79.18
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x512_40k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_40k_voc12aug/deeplabv3_r101-d8_512x512_40k_voc12aug_20200613_161432-0017d784.pth
- Name: deeplabv3_r101-d8_480x480_40k_pascal_context
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (480,480)
    lr schd: 40000
    inference time (ms/im):
    - value: 141.04
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (480,480)
    Training Memory (GB): 9.2
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal Context
    Metrics:
      mIoU: 46.55
      mIoU(ms+flip): 47.81
  Config: configs/deeplabv3/deeplabv3_r101-d8_480x480_40k_pascal_context.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_40k_pascal_context/deeplabv3_r101-d8_480x480_40k_pascal_context_20200911_204118-1aa27336.pth
- Name: deeplabv3_r101-d8_480x480_80k_pascal_context
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (480,480)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal Context
    Metrics:
      mIoU: 46.42
      mIoU(ms+flip): 47.53
  Config: configs/deeplabv3/deeplabv3_r101-d8_480x480_80k_pascal_context.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_80k_pascal_context/deeplabv3_r101-d8_480x480_80k_pascal_context_20200911_170155-2a21fff3.pth
- Name: deeplabv3_r101-d8_480x480_40k_pascal_context_59
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (480,480)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal Context 59
    Metrics:
      mIoU: 52.61
      mIoU(ms+flip): 54.28
  Config: configs/deeplabv3/deeplabv3_r101-d8_480x480_40k_pascal_context_59.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_40k_pascal_context_59/deeplabv3_r101-d8_480x480_40k_pascal_context_59_20210416_110332-cb08ea46.pth
- Name: deeplabv3_r101-d8_480x480_80k_pascal_context_59
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (480,480)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal Context 59
    Metrics:
      mIoU: 52.46
      mIoU(ms+flip): 54.09
  Config: configs/deeplabv3/deeplabv3_r101-d8_480x480_80k_pascal_context_59.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_480x480_80k_pascal_context_59/deeplabv3_r101-d8_480x480_80k_pascal_context_59_20210416_113002-26303993.pth
- Name: deeplabv3_r50-d8_512x512_4x4_20k_coco-stuff10k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 92.59
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.6
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 34.66
      mIoU(ms+flip): 36.08
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x512_4x4_20k_coco-stuff10k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_20k_coco-stuff10k/deeplabv3_r50-d8_512x512_4x4_20k_coco-stuff10k_20210821_043025-b35f789d.pth
- Name: deeplabv3_r101-d8_512x512_4x4_20k_coco-stuff10k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 114.94
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 13.2
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 37.3
      mIoU(ms+flip): 38.42
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x512_4x4_20k_coco-stuff10k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_20k_coco-stuff10k/deeplabv3_r101-d8_512x512_4x4_20k_coco-stuff10k_20210821_043025-c49752cb.pth
- Name: deeplabv3_r50-d8_512x512_4x4_40k_coco-stuff10k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 35.73
      mIoU(ms+flip): 37.09
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x512_4x4_40k_coco-stuff10k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_40k_coco-stuff10k/deeplabv3_r50-d8_512x512_4x4_40k_coco-stuff10k_20210821_043305-dc76f3ff.pth
- Name: deeplabv3_r101-d8_512x512_4x4_40k_coco-stuff10k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 37.81
      mIoU(ms+flip): 38.8
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x512_4x4_40k_coco-stuff10k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_40k_coco-stuff10k/deeplabv3_r101-d8_512x512_4x4_40k_coco-stuff10k_20210821_043305-636cb433.pth
- Name: deeplabv3_r50-d8_512x512_4x4_80k_coco-stuff164k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 92.59
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.6
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 39.38
      mIoU(ms+flip): 40.03
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x512_4x4_80k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_80k_coco-stuff164k/deeplabv3_r50-d8_512x512_4x4_80k_coco-stuff164k_20210709_163016-88675c24.pth
- Name: deeplabv3_r101-d8_512x512_4x4_80k_coco-stuff164k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 114.94
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 13.2
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 40.87
      mIoU(ms+flip): 41.5
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x512_4x4_80k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_80k_coco-stuff164k/deeplabv3_r101-d8_512x512_4x4_80k_coco-stuff164k_20210709_201252-13600dc2.pth
- Name: deeplabv3_r50-d8_512x512_4x4_160k_coco-stuff164k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 41.09
      mIoU(ms+flip): 41.69
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x512_4x4_160k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_160k_coco-stuff164k/deeplabv3_r50-d8_512x512_4x4_160k_coco-stuff164k_20210709_163016-49f2812b.pth
- Name: deeplabv3_r101-d8_512x512_4x4_160k_coco-stuff164k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 41.82
      mIoU(ms+flip): 42.49
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x512_4x4_160k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_160k_coco-stuff164k/deeplabv3_r101-d8_512x512_4x4_160k_coco-stuff164k_20210709_155402-f035acfd.pth
- Name: deeplabv3_r50-d8_512x512_4x4_320k_coco-stuff164k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 320000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 41.37
      mIoU(ms+flip): 42.22
  Config: configs/deeplabv3/deeplabv3_r50-d8_512x512_4x4_320k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r50-d8_512x512_4x4_320k_coco-stuff164k/deeplabv3_r50-d8_512x512_4x4_320k_coco-stuff164k_20210709_155403-51b21115.pth
- Name: deeplabv3_r101-d8_512x512_4x4_320k_coco-stuff164k
  In Collection: DeepLabV3
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 320000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 42.61
      mIoU(ms+flip): 43.42
  Config: configs/deeplabv3/deeplabv3_r101-d8_512x512_4x4_320k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/deeplabv3/deeplabv3_r101-d8_512x512_4x4_320k_coco-stuff164k/deeplabv3_r101-d8_512x512_4x4_320k_coco-stuff164k_20210709_155402-3cbca14d.pth

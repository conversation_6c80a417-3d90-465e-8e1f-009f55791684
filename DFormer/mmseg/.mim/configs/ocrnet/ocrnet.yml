Collections:
- Name: OCRNet
  Metadata:
    Training Data:
    - Cityscapes
    - ADE20K
    - Pascal VOC 2012 + Aug
  Paper:
    URL: https://arxiv.org/abs/1909.11065
    Title: Object-Contextual Representations for Semantic Segmentation
  README: configs/ocrnet/README.md
  Code:
    URL: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/ocr_head.py#L86
    Version: v0.17.0
  Converted From:
    Code: https://github.com/openseg-group/OCNet.pytorch
Models:
- Name: ocrnet_hr18s_512x1024_40k_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18-Small
    crop size: (512,1024)
    lr schd: 40000
    inference time (ms/im):
    - value: 95.69
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 3.5
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 74.3
      mIoU(ms+flip): 75.95
  Config: configs/ocrnet/ocrnet_hr18s_512x1024_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x1024_40k_cityscapes/ocrnet_hr18s_512x1024_40k_cityscapes_20200601_033304-fa2436c2.pth
- Name: ocrnet_hr18_512x1024_40k_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18
    crop size: (512,1024)
    lr schd: 40000
    inference time (ms/im):
    - value: 133.33
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 4.7
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.72
      mIoU(ms+flip): 79.49
  Config: configs/ocrnet/ocrnet_hr18_512x1024_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x1024_40k_cityscapes/ocrnet_hr18_512x1024_40k_cityscapes_20200601_033320-401c5bdd.pth
- Name: ocrnet_hr48_512x1024_40k_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W48
    crop size: (512,1024)
    lr schd: 40000
    inference time (ms/im):
    - value: 236.97
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 8.0
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.58
      mIoU(ms+flip): 81.79
  Config: configs/ocrnet/ocrnet_hr48_512x1024_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x1024_40k_cityscapes/ocrnet_hr48_512x1024_40k_cityscapes_20200601_033336-55b32491.pth
- Name: ocrnet_hr18s_512x1024_80k_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18-Small
    crop size: (512,1024)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.16
      mIoU(ms+flip): 78.66
  Config: configs/ocrnet/ocrnet_hr18s_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x1024_80k_cityscapes/ocrnet_hr18s_512x1024_80k_cityscapes_20200601_222735-55979e63.pth
- Name: ocrnet_hr18_512x1024_80k_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18
    crop size: (512,1024)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.57
      mIoU(ms+flip): 80.46
  Config: configs/ocrnet/ocrnet_hr18_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x1024_80k_cityscapes/ocrnet_hr18_512x1024_80k_cityscapes_20200614_230521-c2e1dd4a.pth
- Name: ocrnet_hr48_512x1024_80k_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W48
    crop size: (512,1024)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.7
      mIoU(ms+flip): 81.87
  Config: configs/ocrnet/ocrnet_hr48_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x1024_80k_cityscapes/ocrnet_hr48_512x1024_80k_cityscapes_20200601_222752-9076bcdf.pth
- Name: ocrnet_hr18s_512x1024_160k_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18-Small
    crop size: (512,1024)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.45
      mIoU(ms+flip): 79.97
  Config: configs/ocrnet/ocrnet_hr18s_512x1024_160k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x1024_160k_cityscapes/ocrnet_hr18s_512x1024_160k_cityscapes_20200602_191005-f4a7af28.pth
- Name: ocrnet_hr18_512x1024_160k_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18
    crop size: (512,1024)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.47
      mIoU(ms+flip): 80.91
  Config: configs/ocrnet/ocrnet_hr18_512x1024_160k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x1024_160k_cityscapes/ocrnet_hr18_512x1024_160k_cityscapes_20200602_191001-b9172d0c.pth
- Name: ocrnet_hr48_512x1024_160k_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W48
    crop size: (512,1024)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 81.35
      mIoU(ms+flip): 82.7
  Config: configs/ocrnet/ocrnet_hr48_512x1024_160k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x1024_160k_cityscapes/ocrnet_hr48_512x1024_160k_cityscapes_20200602_191037-dfbf1b0c.pth
- Name: ocrnet_r101-d8_512x1024_40k_b8_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,1024)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.09
  Config: configs/ocrnet/ocrnet_r101-d8_512x1024_40k_b8_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_r101-d8_512x1024_40k_b8_cityscapes/ocrnet_r101-d8_512x1024_40k_b8_cityscapes_20200717_110721-02ac0f13.pth
- Name: ocrnet_r101-d8_512x1024_40k_b16_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,1024)
    lr schd: 40000
    inference time (ms/im):
    - value: 331.13
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 8.8
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.3
  Config: configs/ocrnet/ocrnet_r101-d8_512x1024_40k_b16_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_r101-d8_512x1024_40k_b16_cityscapes/ocrnet_r101-d8_512x1024_40k_b16_cityscapes_20200723_193726-db500f80.pth
- Name: ocrnet_r101-d8_512x1024_80k_b16_cityscapes
  In Collection: OCRNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 331.13
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 8.8
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 80.81
  Config: configs/ocrnet/ocrnet_r101-d8_512x1024_80k_b16_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_r101-d8_512x1024_80k_b16_cityscapes/ocrnet_r101-d8_512x1024_80k_b16_cityscapes_20200723_192421-78688424.pth
- Name: ocrnet_hr18s_512x512_80k_ade20k
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18-Small
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 34.51
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.7
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 35.06
      mIoU(ms+flip): 35.8
  Config: configs/ocrnet/ocrnet_hr18s_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_80k_ade20k/ocrnet_hr18s_512x512_80k_ade20k_20200615_055600-e80b62af.pth
- Name: ocrnet_hr18_512x512_80k_ade20k
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 52.83
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 7.9
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 37.79
      mIoU(ms+flip): 39.16
  Config: configs/ocrnet/ocrnet_hr18_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_80k_ade20k/ocrnet_hr18_512x512_80k_ade20k_20200615_053157-d173d83b.pth
- Name: ocrnet_hr48_512x512_80k_ade20k
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W48
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 58.86
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 11.2
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.0
      mIoU(ms+flip): 44.3
  Config: configs/ocrnet/ocrnet_hr48_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_80k_ade20k/ocrnet_hr48_512x512_80k_ade20k_20200615_021518-d168c2d1.pth
- Name: ocrnet_hr18s_512x512_160k_ade20k
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18-Small
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 37.19
      mIoU(ms+flip): 38.4
  Config: configs/ocrnet/ocrnet_hr18s_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_160k_ade20k/ocrnet_hr18s_512x512_160k_ade20k_20200615_184505-8e913058.pth
- Name: ocrnet_hr18_512x512_160k_ade20k
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 39.32
      mIoU(ms+flip): 40.8
  Config: configs/ocrnet/ocrnet_hr18_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_160k_ade20k/ocrnet_hr18_512x512_160k_ade20k_20200615_200940-d8fcd9d1.pth
- Name: ocrnet_hr48_512x512_160k_ade20k
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W48
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.25
      mIoU(ms+flip): 44.88
  Config: configs/ocrnet/ocrnet_hr48_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_160k_ade20k/ocrnet_hr48_512x512_160k_ade20k_20200615_184705-a073726d.pth
- Name: ocrnet_hr18s_512x512_20k_voc12aug
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18-Small
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 31.7
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 3.5
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 71.7
      mIoU(ms+flip): 73.84
  Config: configs/ocrnet/ocrnet_hr18s_512x512_20k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_20k_voc12aug/ocrnet_hr18s_512x512_20k_voc12aug_20200617_233913-02b04fcb.pth
- Name: ocrnet_hr18_512x512_20k_voc12aug
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 50.23
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 4.7
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 74.75
      mIoU(ms+flip): 77.11
  Config: configs/ocrnet/ocrnet_hr18_512x512_20k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_20k_voc12aug/ocrnet_hr18_512x512_20k_voc12aug_20200617_233932-8954cbb7.pth
- Name: ocrnet_hr48_512x512_20k_voc12aug
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W48
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 56.09
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 8.1
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.72
      mIoU(ms+flip): 79.87
  Config: configs/ocrnet/ocrnet_hr48_512x512_20k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_20k_voc12aug/ocrnet_hr48_512x512_20k_voc12aug_20200617_233932-9e82080a.pth
- Name: ocrnet_hr18s_512x512_40k_voc12aug
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18-Small
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 72.76
      mIoU(ms+flip): 74.6
  Config: configs/ocrnet/ocrnet_hr18s_512x512_40k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18s_512x512_40k_voc12aug/ocrnet_hr18s_512x512_40k_voc12aug_20200614_002025-42b587ac.pth
- Name: ocrnet_hr18_512x512_40k_voc12aug
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W18
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 74.98
      mIoU(ms+flip): 77.4
  Config: configs/ocrnet/ocrnet_hr18_512x512_40k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr18_512x512_40k_voc12aug/ocrnet_hr18_512x512_40k_voc12aug_20200614_015958-714302be.pth
- Name: ocrnet_hr48_512x512_40k_voc12aug
  In Collection: OCRNet
  Metadata:
    backbone: HRNetV2p-W48
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.14
      mIoU(ms+flip): 79.71
  Config: configs/ocrnet/ocrnet_hr48_512x512_40k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/ocrnet/ocrnet_hr48_512x512_40k_voc12aug/ocrnet_hr48_512x512_40k_voc12aug_20200614_015958-255bc5ce.pth

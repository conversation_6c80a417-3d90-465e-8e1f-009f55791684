Models:
- Name: fpn_poolformer_s12_8x4_512x512_40k_ade20k
  In Collection: FPN
  Metadata:
    backbone: PoolFormer-S12
    crop size: (512,512)
    lr schd: 40000
    inference time (ms/im):
    - value: 42.59
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 4.17
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 36.68
      mIoU(ms+flip): 38.22
  Config: configs/poolformer/fpn_poolformer_s12_8x4_512x512_40k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/poolformer/fpn_poolformer_s12_8x4_512x512_40k_ade20k/fpn_poolformer_s12_8x4_512x512_40k_ade20k_20220501_115154-b5aa2f49.pth
- Name: fpn_poolformer_s24_8x4_512x512_40k_ade20k
  In Collection: FPN
  Metadata:
    backbone: PoolFormer-S24
    crop size: (512,512)
    lr schd: 40000
    inference time (ms/im):
    - value: 63.53
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 5.47
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 40.12
      mIoU(ms+flip): 40.97
  Config: configs/poolformer/fpn_poolformer_s24_8x4_512x512_40k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/poolformer/fpn_poolformer_s24_8x4_512x512_40k_ade20k/fpn_poolformer_s24_8x4_512x512_40k_ade20k_20220503_222049-394a7cf7.pth
- Name: fpn_poolformer_s36_8x4_512x512_40k_ade20k
  In Collection: FPN
  Metadata:
    backbone: PoolFormer-S36
    crop size: (512,512)
    lr schd: 40000
    inference time (ms/im):
    - value: 88.18
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.77
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 41.61
      mIoU(ms+flip): 42.61
  Config: configs/poolformer/fpn_poolformer_s36_8x4_512x512_40k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/poolformer/fpn_poolformer_s36_8x4_512x512_40k_ade20k/fpn_poolformer_s36_8x4_512x512_40k_ade20k_20220501_151122-b47e607d.pth
- Name: fpn_poolformer_m36_8x4_512x512_40k_ade20k
  In Collection: FPN
  Metadata:
    backbone: PoolFormer-M36
    crop size: (512,512)
    lr schd: 40000
    inference time (ms/im):
    - value: 111.48
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 8.59
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 41.95
      mIoU(ms+flip): 43.24
  Config: configs/poolformer/fpn_poolformer_m36_8x4_512x512_40k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/poolformer/fpn_poolformer_m36_8x4_512x512_40k_ade20k/fpn_poolformer_m36_8x4_512x512_40k_ade20k_20220501_164230-3dc83921.pth
- Name: fpn_poolformer_m48_8x4_512x512_40k_ade20k
  In Collection: FPN
  Metadata:
    backbone: PoolFormer-M48
    crop size: (512,512)
    lr schd: 40000
    inference time (ms/im):
    - value: 149.48
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 10.48
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 42.43
      mIoU(ms+flip): 43.6
  Config: configs/poolformer/fpn_poolformer_m48_8x4_512x512_40k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/poolformer/fpn_poolformer_m48_8x4_512x512_40k_ade20k/fpn_poolformer_m48_8x4_512x512_40k_ade20k_20220504_003923-64168d3b.pth

Collections:
- Name: PSPNet
  Metadata:
    Training Data:
    - Cityscapes
    - ADE20K
    - <PERSON> VOC 2012 + Aug
    - <PERSON> Context
    - <PERSON> Context 59
    - Dark Zurich and Nighttime Driving
    - COCO-Stuff 10k
    - COCO-Stuff 164k
    - LoveDA
    - Potsdam
    - Vaihingen
    - iSAID
  Paper:
    URL: https://arxiv.org/abs/1612.01105
    Title: Pyramid Scene Parsing Network
  README: configs/pspnet/README.md
  Code:
    URL: https://github.com/open-mmlab/mmsegmentation/blob/v0.17.0/mmseg/models/decode_heads/psp_head.py#L63
    Version: v0.17.0
  Converted From:
    Code: https://github.com/hszhao/PSPNet
Models:
- Name: pspnet_r50-d8_512x1024_40k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,1024)
    lr schd: 40000
    inference time (ms/im):
    - value: 245.7
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 6.1
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.85
      mIoU(ms+flip): 79.18
  Config: configs/pspnet/pspnet_r50-d8_512x1024_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x1024_40k_cityscapes/pspnet_r50-d8_512x1024_40k_cityscapes_20200605_003338-2966598c.pth
- Name: pspnet_r101-d8_512x1024_40k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,1024)
    lr schd: 40000
    inference time (ms/im):
    - value: 373.13
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 9.6
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.34
      mIoU(ms+flip): 79.74
  Config: configs/pspnet/pspnet_r101-d8_512x1024_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x1024_40k_cityscapes/pspnet_r101-d8_512x1024_40k_cityscapes_20200604_232751-467e7cf4.pth
- Name: pspnet_r50-d8_769x769_40k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (769,769)
    lr schd: 40000
    inference time (ms/im):
    - value: 568.18
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 6.9
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.26
      mIoU(ms+flip): 79.88
  Config: configs/pspnet/pspnet_r50-d8_769x769_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_769x769_40k_cityscapes/pspnet_r50-d8_769x769_40k_cityscapes_20200606_112725-86638686.pth
- Name: pspnet_r101-d8_769x769_40k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (769,769)
    lr schd: 40000
    inference time (ms/im):
    - value: 869.57
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 10.9
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.08
      mIoU(ms+flip): 80.28
  Config: configs/pspnet/pspnet_r101-d8_769x769_40k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_769x769_40k_cityscapes/pspnet_r101-d8_769x769_40k_cityscapes_20200606_112753-61c6f5be.pth
- Name: pspnet_r18-d8_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-18-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 63.65
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 1.7
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 74.87
      mIoU(ms+flip): 76.04
  Config: configs/pspnet/pspnet_r18-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r18-d8_512x1024_80k_cityscapes/pspnet_r18-d8_512x1024_80k_cityscapes_20201225_021458-09ffa746.pth
- Name: pspnet_r50-d8_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,1024)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.55
      mIoU(ms+flip): 79.79
  Config: configs/pspnet/pspnet_r50-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x1024_80k_cityscapes/pspnet_r50-d8_512x1024_80k_cityscapes_20200606_112131-2376f12b.pth
- Name: pspnet_r50-d8_rsb-pretrain_512x1024_adamw_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50b-D8 rsb
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 261.78
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 6.2
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.47
      mIoU(ms+flip): 79.45
  Config: configs/pspnet/pspnet_r50-d8_rsb-pretrain_512x1024_adamw_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_rsb-pretrain_512x1024_adamw_80k_cityscapes/pspnet_r50-d8_rsb-pretrain_512x1024_adamw_80k_cityscapes_20220315_123238-588c30be.pth
- Name: pspnet_r101-d8_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,1024)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.76
      mIoU(ms+flip): 81.01
  Config: configs/pspnet/pspnet_r101-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x1024_80k_cityscapes/pspnet_r101-d8_512x1024_80k_cityscapes_20200606_112211-e1e1100f.pth
- Name: pspnet_r101-d8_fp16_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 114.03
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP16
      resolution: (512,1024)
    Training Memory (GB): 5.34
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.46
  Config: configs/pspnet/pspnet_r101-d8_fp16_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_fp16_512x1024_80k_cityscapes/pspnet_r101-d8_fp16_512x1024_80k_cityscapes_20200717_230919-a0875e5c.pth
- Name: pspnet_r18-d8_769x769_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-18-D8
    crop size: (769,769)
    lr schd: 80000
    inference time (ms/im):
    - value: 161.29
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 1.9
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 75.9
      mIoU(ms+flip): 77.86
  Config: configs/pspnet/pspnet_r18-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r18-d8_769x769_80k_cityscapes/pspnet_r18-d8_769x769_80k_cityscapes_20201225_021458-3deefc62.pth
- Name: pspnet_r50-d8_769x769_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (769,769)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.59
      mIoU(ms+flip): 80.69
  Config: configs/pspnet/pspnet_r50-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_769x769_80k_cityscapes/pspnet_r50-d8_769x769_80k_cityscapes_20200606_210121-5ccf03dd.pth
- Name: pspnet_r101-d8_769x769_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (769,769)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.77
      mIoU(ms+flip): 81.06
  Config: configs/pspnet/pspnet_r101-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_769x769_80k_cityscapes/pspnet_r101-d8_769x769_80k_cityscapes_20200606_225055-dba412fa.pth
- Name: pspnet_r18b-d8_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-18b-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 61.43
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 1.5
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 74.23
      mIoU(ms+flip): 75.79
  Config: configs/pspnet/pspnet_r18b-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r18b-d8_512x1024_80k_cityscapes/pspnet_r18b-d8_512x1024_80k_cityscapes_20201226_063116-26928a60.pth
- Name: pspnet_r50b-d8_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50b-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 232.56
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 6.0
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.22
      mIoU(ms+flip): 79.46
  Config: configs/pspnet/pspnet_r50b-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50b-d8_512x1024_80k_cityscapes/pspnet_r50b-d8_512x1024_80k_cityscapes_20201225_094315-6344287a.pth
- Name: pspnet_r101b-d8_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-101b-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 362.32
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 9.5
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.69
      mIoU(ms+flip): 80.79
  Config: configs/pspnet/pspnet_r101b-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101b-d8_512x1024_80k_cityscapes/pspnet_r101b-d8_512x1024_80k_cityscapes_20201226_170012-3a4d38ab.pth
- Name: pspnet_r18b-d8_769x769_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-18b-D8
    crop size: (769,769)
    lr schd: 80000
    inference time (ms/im):
    - value: 156.01
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 1.7
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 74.92
      mIoU(ms+flip): 76.9
  Config: configs/pspnet/pspnet_r18b-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r18b-d8_769x769_80k_cityscapes/pspnet_r18b-d8_769x769_80k_cityscapes_20201226_080942-bf98d186.pth
- Name: pspnet_r50b-d8_769x769_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50b-D8
    crop size: (769,769)
    lr schd: 80000
    inference time (ms/im):
    - value: 531.91
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 6.8
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.5
      mIoU(ms+flip): 79.96
  Config: configs/pspnet/pspnet_r50b-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50b-d8_769x769_80k_cityscapes/pspnet_r50b-d8_769x769_80k_cityscapes_20201225_094316-4c643cf6.pth
- Name: pspnet_r101b-d8_769x769_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-101b-D8
    crop size: (769,769)
    lr schd: 80000
    inference time (ms/im):
    - value: 854.7
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (769,769)
    Training Memory (GB): 10.8
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.87
      mIoU(ms+flip): 80.04
  Config: configs/pspnet/pspnet_r101b-d8_769x769_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101b-d8_769x769_80k_cityscapes/pspnet_r101b-d8_769x769_80k_cityscapes_20201226_171823-f0e7c293.pth
- Name: pspnet_r50-d32_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D32
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 65.75
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 3.0
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 73.88
      mIoU(ms+flip): 76.85
  Config: configs/pspnet/pspnet_r50-d32_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d32_512x1024_80k_cityscapes/pspnet_r50-d32_512x1024_80k_cityscapes_20220316_224840-9092b254.pth
- Name: pspnet_r50-d32_rsb-pretrain_512x1024_adamw_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50b-D32 rsb
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 62.19
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 3.1
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 74.09
      mIoU(ms+flip): 77.18
  Config: configs/pspnet/pspnet_r50-d32_rsb-pretrain_512x1024_adamw_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d32_rsb-pretrain_512x1024_adamw_80k_cityscapes/pspnet_r50-d32_rsb-pretrain_512x1024_adamw_80k_cityscapes_20220316_141229-dd9c9610.pth
- Name: pspnet_r50b-d32_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: R-50b-D32
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 64.89
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 2.9
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 72.61
      mIoU(ms+flip): 75.51
  Config: configs/pspnet/pspnet_r50b-d32_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50b-d32_512x1024_80k_cityscapes/pspnet_r50b-d32_512x1024_80k_cityscapes_20220311_152152-23bcaf8c.pth
- Name: pspnet_r50-d8_512x512_80k_ade20k
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 42.5
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 8.5
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 41.13
      mIoU(ms+flip): 41.94
  Config: configs/pspnet/pspnet_r50-d8_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_80k_ade20k/pspnet_r50-d8_512x512_80k_ade20k_20200615_014128-15a8b914.pth
- Name: pspnet_r101-d8_512x512_80k_ade20k
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 65.36
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 12.0
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.57
      mIoU(ms+flip): 44.35
  Config: configs/pspnet/pspnet_r101-d8_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_80k_ade20k/pspnet_r101-d8_512x512_80k_ade20k_20200614_031423-b6e782f0.pth
- Name: pspnet_r50-d8_512x512_160k_ade20k
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 42.48
      mIoU(ms+flip): 43.44
  Config: configs/pspnet/pspnet_r50-d8_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_160k_ade20k/pspnet_r50-d8_512x512_160k_ade20k_20200615_184358-1890b0bd.pth
- Name: pspnet_r101-d8_512x512_160k_ade20k
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 44.39
      mIoU(ms+flip): 45.35
  Config: configs/pspnet/pspnet_r101-d8_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_160k_ade20k/pspnet_r101-d8_512x512_160k_ade20k_20200615_100650-967c316f.pth
- Name: pspnet_r50-d8_512x512_20k_voc12aug
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 42.39
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.1
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 76.78
      mIoU(ms+flip): 77.61
  Config: configs/pspnet/pspnet_r50-d8_512x512_20k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_20k_voc12aug/pspnet_r50-d8_512x512_20k_voc12aug_20200617_101958-ed5dfbd9.pth
- Name: pspnet_r101-d8_512x512_20k_voc12aug
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 66.58
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.6
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 78.47
      mIoU(ms+flip): 79.25
  Config: configs/pspnet/pspnet_r101-d8_512x512_20k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_20k_voc12aug/pspnet_r101-d8_512x512_20k_voc12aug_20200617_102003-4aef3c9a.pth
- Name: pspnet_r50-d8_512x512_40k_voc12aug
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 77.29
      mIoU(ms+flip): 78.48
  Config: configs/pspnet/pspnet_r50-d8_512x512_40k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_40k_voc12aug/pspnet_r50-d8_512x512_40k_voc12aug_20200613_161222-ae9c1b8c.pth
- Name: pspnet_r101-d8_512x512_40k_voc12aug
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal VOC 2012 + Aug
    Metrics:
      mIoU: 78.52
      mIoU(ms+flip): 79.57
  Config: configs/pspnet/pspnet_r101-d8_512x512_40k_voc12aug.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_40k_voc12aug/pspnet_r101-d8_512x512_40k_voc12aug_20200613_161222-bc933b18.pth
- Name: pspnet_r101-d8_480x480_40k_pascal_context
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (480,480)
    lr schd: 40000
    inference time (ms/im):
    - value: 103.31
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (480,480)
    Training Memory (GB): 8.8
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal Context
    Metrics:
      mIoU: 46.6
      mIoU(ms+flip): 47.78
  Config: configs/pspnet/pspnet_r101-d8_480x480_40k_pascal_context.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_480x480_40k_pascal_context/pspnet_r101-d8_480x480_40k_pascal_context_20200911_211210-bf0f5d7c.pth
- Name: pspnet_r101-d8_480x480_80k_pascal_context
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (480,480)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal Context
    Metrics:
      mIoU: 46.03
      mIoU(ms+flip): 47.15
  Config: configs/pspnet/pspnet_r101-d8_480x480_80k_pascal_context.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_480x480_80k_pascal_context/pspnet_r101-d8_480x480_80k_pascal_context_20200911_190530-c86d6233.pth
- Name: pspnet_r101-d8_480x480_40k_pascal_context_59
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (480,480)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal Context 59
    Metrics:
      mIoU: 52.02
      mIoU(ms+flip): 53.54
  Config: configs/pspnet/pspnet_r101-d8_480x480_40k_pascal_context_59.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_480x480_40k_pascal_context_59/pspnet_r101-d8_480x480_40k_pascal_context_59_20210416_114524-86d44cd4.pth
- Name: pspnet_r101-d8_480x480_80k_pascal_context_59
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (480,480)
    lr schd: 80000
  Results:
  - Task: Semantic Segmentation
    Dataset: Pascal Context 59
    Metrics:
      mIoU: 52.47
      mIoU(ms+flip): 53.99
  Config: configs/pspnet/pspnet_r101-d8_480x480_80k_pascal_context_59.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_480x480_80k_pascal_context_59/pspnet_r101-d8_480x480_80k_pascal_context_59_20210416_114418-fa6caaa2.pth
- Name: pspnet_r50-d8_512x512_4x4_20k_coco-stuff10k
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 48.78
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.6
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 35.69
      mIoU(ms+flip): 36.62
  Config: configs/pspnet/pspnet_r50-d8_512x512_4x4_20k_coco-stuff10k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_4x4_20k_coco-stuff10k/pspnet_r50-d8_512x512_4x4_20k_coco-stuff10k_20210820_203258-b88df27f.pth
- Name: pspnet_r101-d8_512x512_4x4_20k_coco-stuff10k
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 20000
    inference time (ms/im):
    - value: 90.09
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 13.2
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 37.26
      mIoU(ms+flip): 38.52
  Config: configs/pspnet/pspnet_r101-d8_512x512_4x4_20k_coco-stuff10k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_4x4_20k_coco-stuff10k/pspnet_r101-d8_512x512_4x4_20k_coco-stuff10k_20210820_232135-76aae482.pth
- Name: pspnet_r50-d8_512x512_4x4_40k_coco-stuff10k
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 36.33
      mIoU(ms+flip): 37.24
  Config: configs/pspnet/pspnet_r50-d8_512x512_4x4_40k_coco-stuff10k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_4x4_40k_coco-stuff10k/pspnet_r50-d8_512x512_4x4_40k_coco-stuff10k_20210821_030857-92e2902b.pth
- Name: pspnet_r101-d8_512x512_4x4_40k_coco-stuff10k
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 40000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 10k
    Metrics:
      mIoU: 37.76
      mIoU(ms+flip): 38.86
  Config: configs/pspnet/pspnet_r101-d8_512x512_4x4_40k_coco-stuff10k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_4x4_40k_coco-stuff10k/pspnet_r101-d8_512x512_4x4_40k_coco-stuff10k_20210821_014022-831aec95.pth
- Name: pspnet_r50-d8_512x512_4x4_80k_coco-stuff164k
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 48.78
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.6
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 38.8
      mIoU(ms+flip): 39.19
  Config: configs/pspnet/pspnet_r50-d8_512x512_4x4_80k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_4x4_80k_coco-stuff164k/pspnet_r50-d8_512x512_4x4_80k_coco-stuff164k_20210707_152034-0e41b2db.pth
- Name: pspnet_r101-d8_512x512_4x4_80k_coco-stuff164k
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 90.09
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 13.2
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 40.34
      mIoU(ms+flip): 40.79
  Config: configs/pspnet/pspnet_r101-d8_512x512_4x4_80k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_4x4_80k_coco-stuff164k/pspnet_r101-d8_512x512_4x4_80k_coco-stuff164k_20210707_152034-7eb41789.pth
- Name: pspnet_r50-d8_512x512_4x4_160k_coco-stuff164k
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 39.64
      mIoU(ms+flip): 39.97
  Config: configs/pspnet/pspnet_r50-d8_512x512_4x4_160k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_4x4_160k_coco-stuff164k/pspnet_r50-d8_512x512_4x4_160k_coco-stuff164k_20210707_152004-51276a57.pth
- Name: pspnet_r101-d8_512x512_4x4_160k_coco-stuff164k
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 41.28
      mIoU(ms+flip): 41.66
  Config: configs/pspnet/pspnet_r101-d8_512x512_4x4_160k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_4x4_160k_coco-stuff164k/pspnet_r101-d8_512x512_4x4_160k_coco-stuff164k_20210707_152004-4af9621b.pth
- Name: pspnet_r50-d8_512x512_4x4_320k_coco-stuff164k
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 320000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 40.53
      mIoU(ms+flip): 40.75
  Config: configs/pspnet/pspnet_r50-d8_512x512_4x4_320k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_4x4_320k_coco-stuff164k/pspnet_r50-d8_512x512_4x4_320k_coco-stuff164k_20210707_152004-be9610cc.pth
- Name: pspnet_r101-d8_512x512_4x4_320k_coco-stuff164k
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 320000
  Results:
  - Task: Semantic Segmentation
    Dataset: COCO-Stuff 164k
    Metrics:
      mIoU: 41.95
      mIoU(ms+flip): 42.42
  Config: configs/pspnet/pspnet_r101-d8_512x512_4x4_320k_coco-stuff164k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_4x4_320k_coco-stuff164k/pspnet_r101-d8_512x512_4x4_320k_coco-stuff164k_20210707_152004-72220c60.pth
- Name: pspnet_r18-d8_512x512_80k_loveda
  In Collection: PSPNet
  Metadata:
    backbone: R-18-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 37.22
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 1.45
  Results:
  - Task: Semantic Segmentation
    Dataset: LoveDA
    Metrics:
      mIoU: 48.62
      mIoU(ms+flip): 47.57
  Config: configs/pspnet/pspnet_r18-d8_512x512_80k_loveda.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r18-d8_512x512_80k_loveda/pspnet_r18-d8_512x512_80k_loveda_20211105_052100-b97697f1.pth
- Name: pspnet_r50-d8_512x512_80k_loveda
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 151.52
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.14
  Results:
  - Task: Semantic Segmentation
    Dataset: LoveDA
    Metrics:
      mIoU: 50.46
      mIoU(ms+flip): 50.19
  Config: configs/pspnet/pspnet_r50-d8_512x512_80k_loveda.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_512x512_80k_loveda/pspnet_r50-d8_512x512_80k_loveda_20211104_155728-88610f9f.pth
- Name: pspnet_r101-d8_512x512_80k_loveda
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 218.34
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.61
  Results:
  - Task: Semantic Segmentation
    Dataset: LoveDA
    Metrics:
      mIoU: 51.86
      mIoU(ms+flip): 51.34
  Config: configs/pspnet/pspnet_r101-d8_512x512_80k_loveda.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_512x512_80k_loveda/pspnet_r101-d8_512x512_80k_loveda_20211104_153212-1c06c6a8.pth
- Name: pspnet_r18-d8_4x4_512x512_80k_potsdam
  In Collection: PSPNet
  Metadata:
    backbone: R-18-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 11.75
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 1.5
  Results:
  - Task: Semantic Segmentation
    Dataset: Potsdam
    Metrics:
      mIoU: 77.09
      mIoU(ms+flip): 78.3
  Config: configs/pspnet/pspnet_r18-d8_4x4_512x512_80k_potsdam.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r18-d8_4x4_512x512_80k_potsdam/pspnet_r18-d8_4x4_512x512_80k_potsdam_20211220_125612-7cd046e1.pth
- Name: pspnet_r50-d8_4x4_512x512_80k_potsdam
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 33.1
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.14
  Results:
  - Task: Semantic Segmentation
    Dataset: Potsdam
    Metrics:
      mIoU: 78.12
      mIoU(ms+flip): 78.98
  Config: configs/pspnet/pspnet_r50-d8_4x4_512x512_80k_potsdam.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_4x4_512x512_80k_potsdam/pspnet_r50-d8_4x4_512x512_80k_potsdam_20211219_043541-2dd5fe67.pth
- Name: pspnet_r101-d8_4x4_512x512_80k_potsdam
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 51.55
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.61
  Results:
  - Task: Semantic Segmentation
    Dataset: Potsdam
    Metrics:
      mIoU: 78.62
      mIoU(ms+flip): 79.47
  Config: configs/pspnet/pspnet_r101-d8_4x4_512x512_80k_potsdam.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_4x4_512x512_80k_potsdam/pspnet_r101-d8_4x4_512x512_80k_potsdam_20211220_125612-aed036c4.pth
- Name: pspnet_r18-d8_4x4_512x512_80k_vaihingen
  In Collection: PSPNet
  Metadata:
    backbone: R-18-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 11.76
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 1.45
  Results:
  - Task: Semantic Segmentation
    Dataset: Vaihingen
    Metrics:
      mIoU: 71.46
      mIoU(ms+flip): 73.36
  Config: configs/pspnet/pspnet_r18-d8_4x4_512x512_80k_vaihingen.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r18-d8_4x4_512x512_80k_vaihingen/pspnet_r18-d8_4x4_512x512_80k_vaihingen_20211228_160355-52a8a6f6.pth
- Name: pspnet_r50-d8_4x4_512x512_80k_vaihingen
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 33.01
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.14
  Results:
  - Task: Semantic Segmentation
    Dataset: Vaihingen
    Metrics:
      mIoU: 72.36
      mIoU(ms+flip): 73.75
  Config: configs/pspnet/pspnet_r50-d8_4x4_512x512_80k_vaihingen.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_4x4_512x512_80k_vaihingen/pspnet_r50-d8_4x4_512x512_80k_vaihingen_20211228_160355-382f8f5b.pth
- Name: pspnet_r101-d8_4x4_512x512_80k_vaihingen
  In Collection: PSPNet
  Metadata:
    backbone: R-101-D8
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 50.08
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.61
  Results:
  - Task: Semantic Segmentation
    Dataset: Vaihingen
    Metrics:
      mIoU: 72.61
      mIoU(ms+flip): 74.18
  Config: configs/pspnet/pspnet_r101-d8_4x4_512x512_80k_vaihingen.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r101-d8_4x4_512x512_80k_vaihingen/pspnet_r101-d8_4x4_512x512_80k_vaihingen_20211231_230806-8eba0a09.pth
- Name: pspnet_r18-d8_4x4_896x896_80k_isaid
  In Collection: PSPNet
  Metadata:
    backbone: R-18-D8
    crop size: (896,896)
    lr schd: 80000
    inference time (ms/im):
    - value: 37.16
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (896,896)
    Training Memory (GB): 4.52
  Results:
  - Task: Semantic Segmentation
    Dataset: iSAID
    Metrics:
      mIoU: 60.22
      mIoU(ms+flip): 61.25
  Config: configs/pspnet/pspnet_r18-d8_4x4_896x896_80k_isaid.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r18-d8_4x4_896x896_80k_isaid/pspnet_r18-d8_4x4_896x896_80k_isaid_20220110_180526-e84c0b6a.pth
- Name: pspnet_r50-d8_4x4_896x896_80k_isaid
  In Collection: PSPNet
  Metadata:
    backbone: R-50-D8
    crop size: (896,896)
    lr schd: 80000
    inference time (ms/im):
    - value: 112.61
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (896,896)
    Training Memory (GB): 16.58
  Results:
  - Task: Semantic Segmentation
    Dataset: iSAID
    Metrics:
      mIoU: 65.36
      mIoU(ms+flip): 66.48
  Config: configs/pspnet/pspnet_r50-d8_4x4_896x896_80k_isaid.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/pspnet/pspnet_r50-d8_4x4_896x896_80k_isaid/pspnet_r50-d8_4x4_896x896_80k_isaid_20220110_180629-1f21dc32.pth

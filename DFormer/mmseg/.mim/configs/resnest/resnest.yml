Models:
- Name: fcn_s101-d8_512x1024_80k_cityscapes
  In Collection: FCN
  Metadata:
    backbone: S-101-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 418.41
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 11.4
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 77.56
      mIoU(ms+flip): 78.98
  Config: configs/resnest/fcn_s101-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/resnest/fcn_s101-d8_512x1024_80k_cityscapes/fcn_s101-d8_512x1024_80k_cityscapes_20200807_140631-f8d155b3.pth
- Name: pspnet_s101-d8_512x1024_80k_cityscapes
  In Collection: PSPNet
  Metadata:
    backbone: S-101-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 396.83
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 11.8
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 78.57
      mIoU(ms+flip): 79.19
  Config: configs/resnest/pspnet_s101-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/resnest/pspnet_s101-d8_512x1024_80k_cityscapes/pspnet_s101-d8_512x1024_80k_cityscapes_20200807_140631-c75f3b99.pth
- Name: deeplabv3_s101-d8_512x1024_80k_cityscapes
  In Collection: DeepLabV3
  Metadata:
    backbone: S-101-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 531.91
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 11.9
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.67
      mIoU(ms+flip): 80.51
  Config: configs/resnest/deeplabv3_s101-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/resnest/deeplabv3_s101-d8_512x1024_80k_cityscapes/deeplabv3_s101-d8_512x1024_80k_cityscapes_20200807_144429-b73c4270.pth
- Name: deeplabv3plus_s101-d8_512x1024_80k_cityscapes
  In Collection: DeepLabV3+
  Metadata:
    backbone: S-101-D8
    crop size: (512,1024)
    lr schd: 80000
    inference time (ms/im):
    - value: 423.73
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,1024)
    Training Memory (GB): 13.2
  Results:
  - Task: Semantic Segmentation
    Dataset: Cityscapes
    Metrics:
      mIoU: 79.62
      mIoU(ms+flip): 80.27
  Config: configs/resnest/deeplabv3plus_s101-d8_512x1024_80k_cityscapes.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/resnest/deeplabv3plus_s101-d8_512x1024_80k_cityscapes/deeplabv3plus_s101-d8_512x1024_80k_cityscapes_20200807_144429-1239eb43.pth
- Name: fcn_s101-d8_512x512_160k_ade20k
  In Collection: FCN
  Metadata:
    backbone: S-101-D8
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 77.76
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 14.2
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 45.62
      mIoU(ms+flip): 46.16
  Config: configs/resnest/fcn_s101-d8_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/resnest/fcn_s101-d8_512x512_160k_ade20k/fcn_s101-d8_512x512_160k_ade20k_20200807_145416-d3160329.pth
- Name: pspnet_s101-d8_512x512_160k_ade20k
  In Collection: PSPNet
  Metadata:
    backbone: S-101-D8
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 76.8
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 14.2
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 45.44
      mIoU(ms+flip): 46.28
  Config: configs/resnest/pspnet_s101-d8_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/resnest/pspnet_s101-d8_512x512_160k_ade20k/pspnet_s101-d8_512x512_160k_ade20k_20200807_145416-a6daa92a.pth
- Name: deeplabv3_s101-d8_512x512_160k_ade20k
  In Collection: DeepLabV3
  Metadata:
    backbone: S-101-D8
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 107.76
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 14.6
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 45.71
      mIoU(ms+flip): 46.59
  Config: configs/resnest/deeplabv3_s101-d8_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/resnest/deeplabv3_s101-d8_512x512_160k_ade20k/deeplabv3_s101-d8_512x512_160k_ade20k_20200807_144503-17ecabe5.pth
- Name: deeplabv3plus_s101-d8_512x512_160k_ade20k
  In Collection: DeepLabV3+
  Metadata:
    backbone: S-101-D8
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 83.61
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 16.2
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 46.47
      mIoU(ms+flip): 47.27
  Config: configs/resnest/deeplabv3plus_s101-d8_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/resnest/deeplabv3plus_s101-d8_512x512_160k_ade20k/deeplabv3plus_s101-d8_512x512_160k_ade20k_20200807_144503-27b26226.pth

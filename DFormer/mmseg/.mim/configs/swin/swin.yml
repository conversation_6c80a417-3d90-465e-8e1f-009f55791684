Models:
- Name: upernet_swin_tiny_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K
  In Collection: UPerNet
  Metadata:
    backbone: Swin-T
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 47.48
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 5.02
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 44.41
      mIoU(ms+flip): 45.79
  Config: configs/swin/upernet_swin_tiny_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/swin/upernet_swin_tiny_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K/upernet_swin_tiny_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K_20210531_112542-e380ad3e.pth
- Name: upernet_swin_small_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K
  In Collection: UPerNet
  Metadata:
    backbone: Swin-S
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 67.93
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.17
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 47.72
      mIoU(ms+flip): 49.24
  Config: configs/swin/upernet_swin_small_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/swin/upernet_swin_small_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K/upernet_swin_small_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K_20210526_192015-ee2fff1c.pth
- Name: upernet_swin_base_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K
  In Collection: UPerNet
  Metadata:
    backbone: Swin-B
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 79.05
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 7.61
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 47.99
      mIoU(ms+flip): 49.57
  Config: configs/swin/upernet_swin_base_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/swin/upernet_swin_base_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K/upernet_swin_base_patch4_window7_512x512_160k_ade20k_pretrain_224x224_1K_20210526_192340-593b0e13.pth
- Name: upernet_swin_base_patch4_window7_512x512_160k_ade20k_pretrain_224x224_22K
  In Collection: UPerNet
  Metadata:
    backbone: Swin-B
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 50.13
      mIoU(ms+flip): 51.9
  Config: configs/swin/upernet_swin_base_patch4_window7_512x512_160k_ade20k_pretrain_224x224_22K.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/swin/upernet_swin_base_patch4_window7_512x512_160k_ade20k_pretrain_224x224_22K/upernet_swin_base_patch4_window7_512x512_160k_ade20k_pretrain_224x224_22K_20210526_211650-762e2178.pth
- Name: upernet_swin_base_patch4_window12_512x512_160k_ade20k_pretrain_384x384_1K
  In Collection: UPerNet
  Metadata:
    backbone: Swin-B
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 82.64
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 8.52
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 48.35
      mIoU(ms+flip): 49.65
  Config: configs/swin/upernet_swin_base_patch4_window12_512x512_160k_ade20k_pretrain_384x384_1K.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/swin/upernet_swin_base_patch4_window12_512x512_160k_ade20k_pretrain_384x384_1K/upernet_swin_base_patch4_window12_512x512_160k_ade20k_pretrain_384x384_1K_20210531_132020-05b22ea4.pth
- Name: upernet_swin_base_patch4_window12_512x512_160k_ade20k_pretrain_384x384_22K
  In Collection: UPerNet
  Metadata:
    backbone: Swin-B
    crop size: (512,512)
    lr schd: 160000
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 50.76
      mIoU(ms+flip): 52.4
  Config: configs/swin/upernet_swin_base_patch4_window12_512x512_160k_ade20k_pretrain_384x384_22K.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/swin/upernet_swin_base_patch4_window12_512x512_160k_ade20k_pretrain_384x384_22K/upernet_swin_base_patch4_window12_512x512_160k_ade20k_pretrain_384x384_22K_20210531_125459-429057bf.pth
- Name: upernet_swin_large_patch4_window7_512x512_pretrain_224x224_22K_160k_ade20k
  In Collection: UPerNet
  Metadata:
    backbone: Swin-L
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 121.51
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 10.98
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 51.17
      mIoU(ms+flip): 52.99
  Config: configs/swin/upernet_swin_large_patch4_window7_512x512_pretrain_224x224_22K_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/swin/upernet_swin_large_patch4_window7_512x512_pretrain_224x224_22K_160k_ade20k/upernet_swin_large_patch4_window7_512x512_pretrain_224x224_22K_160k_ade20k_20220318_015320-48d180dd.pth
- Name: upernet_swin_large_patch4_window12_512x512_pretrain_384x384_22K_160k_ade20k
  In Collection: UPerNet
  Metadata:
    backbone: Swin-L
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 132.1
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 12.42
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 52.25
      mIoU(ms+flip): 54.12
  Config: configs/swin/upernet_swin_large_patch4_window12_512x512_pretrain_384x384_22K_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/swin/upernet_swin_large_patch4_window12_512x512_pretrain_384x384_22K_160k_ade20k/upernet_swin_large_patch4_window12_512x512_pretrain_384x384_22K_160k_ade20k_20220318_091743-9ba68901.pth

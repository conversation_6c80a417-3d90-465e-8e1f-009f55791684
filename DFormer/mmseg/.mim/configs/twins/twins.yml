Models:
- Name: twins_pcpvt-s_fpn_fpnhead_8x4_512x512_80k_ade20k
  In Collection: FPN
  Metadata:
    backbone: PCPVT-S
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 36.83
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.6
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 43.26
      mIoU(ms+flip): 44.11
  Config: configs/twins/twins_pcpvt-s_fpn_fpnhead_8x4_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_pcpvt-s_fpn_fpnhead_8x4_512x512_80k_ade20k/twins_pcpvt-s_fpn_fpnhead_8x4_512x512_80k_ade20k_20211201_204132-41acd132.pth
- Name: twins_pcpvt-s_uperhead_8x4_512x512_160k_ade20k
  In Collection: UPerNet
  Metadata:
    backbone: PCPVT-S
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 70.22
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 9.67
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 46.04
      mIoU(ms+flip): 46.92
  Config: configs/twins/twins_pcpvt-s_uperhead_8x4_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_pcpvt-s_uperhead_8x4_512x512_160k_ade20k/twins_pcpvt-s_uperhead_8x4_512x512_160k_ade20k_20211201_233537-8e99c07a.pth
- Name: twins_pcpvt-b_fpn_fpnhead_8x4_512x512_80k_ade20k
  In Collection: FPN
  Metadata:
    backbone: PCPVT-B
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 50.84
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 8.41
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 45.66
      mIoU(ms+flip): 46.48
  Config: configs/twins/twins_pcpvt-b_fpn_fpnhead_8x4_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_pcpvt-b_fpn_fpnhead_8x4_512x512_80k_ade20k/twins_pcpvt-b_fpn_fpnhead_8x4_512x512_80k_ade20k_20211130_141019-d396db72.pth
- Name: twins_pcpvt-b_uperhead_8x2_512x512_160k_ade20k
  In Collection: UPerNet
  Metadata:
    backbone: PCPVT-B
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 83.06
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.46
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 47.91
      mIoU(ms+flip): 48.64
  Config: configs/twins/twins_pcpvt-b_uperhead_8x2_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_pcpvt-b_uperhead_8x2_512x512_160k_ade20k/twins_pcpvt-b_uperhead_8x2_512x512_160k_ade20k_20211130_141020-02094ea5.pth
- Name: twins_pcpvt-l_fpn_fpnhead_8x4_512x512_80k_ade20k
  In Collection: FPN
  Metadata:
    backbone: PCPVT-L
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 69.83
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 10.78
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 45.94
      mIoU(ms+flip): 46.7
  Config: configs/twins/twins_pcpvt-l_fpn_fpnhead_8x4_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_pcpvt-l_fpn_fpnhead_8x4_512x512_80k_ade20k/twins_pcpvt-l_fpn_fpnhead_8x4_512x512_80k_ade20k_20211201_105226-bc6d61dc.pth
- Name: twins_pcpvt-l_uperhead_8x2_512x512_160k_ade20k
  In Collection: UPerNet
  Metadata:
    backbone: PCPVT-L
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 93.46
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 7.82
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 49.35
      mIoU(ms+flip): 50.08
  Config: configs/twins/twins_pcpvt-l_uperhead_8x2_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_pcpvt-l_uperhead_8x2_512x512_160k_ade20k/twins_pcpvt-l_uperhead_8x2_512x512_160k_ade20k_20211201_075053-c6095c07.pth
- Name: twins_svt-s_fpn_fpnhead_8x4_512x512_80k_ade20k
  In Collection: FPN
  Metadata:
    backbone: SVT-S
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 33.57
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 5.8
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 44.47
      mIoU(ms+flip): 45.42
  Config: configs/twins/twins_svt-s_fpn_fpnhead_8x4_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_svt-s_fpn_fpnhead_8x4_512x512_80k_ade20k/twins_svt-s_fpn_fpnhead_8x4_512x512_80k_ade20k_20211130_141006-0a0d3317.pth
- Name: twins_svt-s_uperhead_8x2_512x512_160k_ade20k
  In Collection: UPerNet
  Metadata:
    backbone: SVT-S
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 66.27
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 4.93
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 46.08
      mIoU(ms+flip): 46.96
  Config: configs/twins/twins_svt-s_uperhead_8x2_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_svt-s_uperhead_8x2_512x512_160k_ade20k/twins_svt-s_uperhead_8x2_512x512_160k_ade20k_20211130_141005-e48a2d94.pth
- Name: twins_svt-b_fpn_fpnhead_8x4_512x512_80k_ade20k
  In Collection: FPN
  Metadata:
    backbone: SVT-B
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 47.39
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 8.75
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 46.77
      mIoU(ms+flip): 47.47
  Config: configs/twins/twins_svt-b_fpn_fpnhead_8x4_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_svt-b_fpn_fpnhead_8x4_512x512_80k_ade20k/twins_svt-b_fpn_fpnhead_8x4_512x512_80k_ade20k_20211201_113849-88b2907c.pth
- Name: twins_svt-b_uperhead_8x2_512x512_160k_ade20k
  In Collection: UPerNet
  Metadata:
    backbone: SVT-B
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 78.99
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 6.77
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 48.04
      mIoU(ms+flip): 48.87
  Config: configs/twins/twins_svt-b_uperhead_8x2_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_svt-b_uperhead_8x2_512x512_160k_ade20k/twins_svt-b_uperhead_8x2_512x512_160k_ade20k_20211202_040826-0943a1f1.pth
- Name: twins_svt-l_fpn_fpnhead_8x4_512x512_80k_ade20k
  In Collection: FPN
  Metadata:
    backbone: SVT-L
    crop size: (512,512)
    lr schd: 80000
    inference time (ms/im):
    - value: 56.18
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 11.2
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 46.55
      mIoU(ms+flip): 47.74
  Config: configs/twins/twins_svt-l_fpn_fpnhead_8x4_512x512_80k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_svt-l_fpn_fpnhead_8x4_512x512_80k_ade20k/twins_svt-l_fpn_fpnhead_8x4_512x512_80k_ade20k_20211130_141005-1d59bee2.pth
- Name: twins_svt-l_uperhead_8x2_512x512_160k_ade20k
  In Collection: UPerNet
  Metadata:
    backbone: SVT-L
    crop size: (512,512)
    lr schd: 160000
    inference time (ms/im):
    - value: 93.2
      hardware: V100
      backend: PyTorch
      batch size: 1
      mode: FP32
      resolution: (512,512)
    Training Memory (GB): 8.41
  Results:
  - Task: Semantic Segmentation
    Dataset: ADE20K
    Metrics:
      mIoU: 49.65
      mIoU(ms+flip): 50.63
  Config: configs/twins/twins_svt-l_uperhead_8x2_512x512_160k_ade20k.py
  Weights: https://download.openmmlab.com/mmsegmentation/v0.5/twins/twins_svt-l_uperhead_8x2_512x512_160k_ade20k/twins_svt-l_uperhead_8x2_512x512_160k_ade20k_20211130_141005-3e2cae61.pth

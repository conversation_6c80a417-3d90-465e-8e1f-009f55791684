# Copyright (c) OpenMMLab. All rights reserved.
from .ann_head import <PERSON><PERSON><PERSON><PERSON>
from .apc_head import <PERSON><PERSON><PERSON>
from .aspp_head import ASPPHead
from .cc_head import CCHead
from .da_head import DAHead
from .dm_head import DMHead
from .dnl_head import <PERSON>NLHead
from .dpt_head import <PERSON><PERSON>TH<PERSON>
from .ema_head import <PERSON><PERSON><PERSON><PERSON>
from .enc_head import <PERSON><PERSON><PERSON><PERSON>
from .fcn_head import <PERSON><PERSON><PERSON><PERSON>
from .fpn_head import FP<PERSON><PERSON><PERSON>
from .gc_head import <PERSON>CHead
from .isa_head import ISAHead
from .knet_head import IterativeDecodeHead, KernelUpdateHead, KernelUpdator
from .lraspp_head import LRASPPHead
from .nl_head import NLHead
from .ocr_head import <PERSON>CRHead
from .point_head import PointHead
from .psa_head import PSAHead
from .psp_head import PSPHead
from .segformer_head import SegformerHead
from .segmenter_mask_head import SegmenterMaskTransformerHead
from .sep_aspp_head import DepthwiseSeparableASPPHead
from .sep_fcn_head import DepthwiseSeparableFCNHead
from .setr_mla_head import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .setr_up_head import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .stdc_head import <PERSON><PERSON>Head
from .uper_head import UPerHead
from .mix_conv_head import MixConvHead

__all__ = [
    "FCNHead",
    "PSPHead",
    "ASPPHead",
    "PSAHead",
    "NLHead",
    "GCHead",
    "CCHead",
    "UPerHead",
    "DepthwiseSeparableASPPHead",
    "ANNHead",
    "DAHead",
    "OCRHead",
    "EncHead",
    "DepthwiseSeparableFCNHead",
    "FPNHead",
    "EMAHead",
    "DNLHead",
    "PointHead",
    "APCHead",
    "DMHead",
    "LRASPPHead",
    "SETRUPHead",
    "SETRMLAHead",
    "DPTHead",
    "SETRMLAHead",
    "SegmenterMaskTransformerHead",
    "SegformerHead",
    "ISAHead",
    "STDCHead",
    "IterativeDecodeHead",
    "KernelUpdateHead",
    "KernelUpdator",
    "MixConvHead",
]

#!/usr/bin/env python3
# encoding: utf-8
# @Time    : 2018/8/2 上午11:48
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
# @Contact : <EMAIL>
# @File    : logger.py
import os
import sys
import logging

from utils import pyt_utils
# from utils.pyt_utils import ensure_dir

_default_level_name = os.getenv("ENGINE_LOGGING_LEVEL", "INFO")
_default_level = logging.getLevelName(_default_level_name.upper())


class LogFormatter(logging.Formatter):
    log_fout = None
    date_full = "[%(asctime)s %(lineno)d@%(filename)s:%(name)s] "
    date = "%(asctime)s "
    msg = "%(message)s"

    def format(self, record):
        if record.levelno == logging.DEBUG:
            mcl, mtxt = self._color_dbg, "DBG"
        elif record.levelno == logging.WARNING:
            mcl, mtxt = self._color_warn, "WRN"
        elif record.levelno == logging.ERROR:
            mcl, mtxt = self._color_err, "ERR"
        else:
            mcl, mtxt = self._color_normal, ""

        if mtxt:
            mtxt += " "

        if self.log_fout:
            self.__set_fmt(self.date_full + mtxt + self.msg)
            formatted = super(LogFormatter, self).format(record)
            # self.log_fout.write(formatted)
            # self.log_fout.write('\n')
            # self.log_fout.flush()
            return formatted

        self.__set_fmt(self._color_date(self.date) + mcl(mtxt + self.msg))
        formatted = super(LogFormatter, self).format(record)

        return formatted

    if sys.version_info.major < 3:

        def __set_fmt(self, fmt):
            self._fmt = fmt
    else:

        def __set_fmt(self, fmt):
            self._style._fmt = fmt

    @staticmethod
    def _color_dbg(msg):
        return "\x1b[36m{}\x1b[0m".format(msg)

    @staticmethod
    def _color_warn(msg):
        return "\x1b[1;31m{}\x1b[0m".format(msg)

    @staticmethod
    def _color_err(msg):
        return "\x1b[1;4;31m{}\x1b[0m".format(msg)

    @staticmethod
    def _color_omitted(msg):
        return "\x1b[35m{}\x1b[0m".format(msg)

    @staticmethod
    def _color_normal(msg):
        return msg

    @staticmethod
    def _color_date(msg):
        return "\x1b[32m{}\x1b[0m".format(msg)


def get_logger_old(log_dir=None, log_file=None, formatter=LogFormatter):
    logger = logging.getLogger()
    logger.setLevel(_default_level)
    del logger.handlers[:]

    if log_dir and log_file:
        pyt_utils.ensure_dir(log_dir)
        formatter = formatter(datefmt="%d %H:%M:%S")
        formatter.log_fout = True
        file_handler = logging.FileHandler(log_file, mode="a")
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(formatter)
    stream_handler.setLevel(0)
    logger.addHandler(stream_handler)
    return logger


def get_logger(log_dir=None, log_file=None, rank=0):
    logger = logging.getLogger()
    logger.setLevel(level=logging.INFO)
    del logger.handlers[:]
    formatter = logging.Formatter("%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s")

    if rank == 0:
        if log_dir and log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(level=logging.INFO)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        stream_handler = logging.StreamHandler()
        stream_handler.setLevel(logging.DEBUG)
        stream_handler.setFormatter(formatter)

        logger.addHandler(stream_handler)
    return logger
